import { defineConfig } from '@tarojs/cli'

export default defineConfig({
  projectName: 'buyer-app',
  date: '2025-8-22',
  designWidth: 750,
  deviceRatio: {
    640: 2.34 / 2,
    750: 1,
    828: 1.81 / 2
  },
  sourceRoot: 'src',
  outputRoot: 'dist',
  plugins: [],
  defineConstants: {
  },
  copy: {
    patterns: [
      { from: 'src/assets/', to: 'dist/assets/' }
    ],
    options: {
    }
  },
  framework: 'react',
  compiler: 'vite', // 改为使用 Vite 编译器
  cache: {
    enable: false
  },
  mini: {
    postcss: {
      pxtransform: {
        enable: true,
        config: {

        }
      },
      url: {
        enable: true,
        config: {
          limit: 1024
        }
      },
      cssModules: {
        enable: false,
        config: {
          namingPattern: 'module',
          generateScopedName: '[name]__[local]___[hash:base64:5]'
        }
      },
      // 添加 NutUI React Taro 的 PostCSS 插件
      nutuiReactTaroPxToViewport: {
        enable: true,
        config: {
          viewportWidth: 375 // 设计稿宽度，根据实际情况调整
        }
      }
    }
  },
  h5: {
    publicPath: '/',
    staticDirectory: 'static',
    postcss: {
      autoprefixer: {
        enable: true,
        config: {
        }
      },
      cssModules: {
        enable: false,
        config: {
          namingPattern: 'module',
          generateScopedName: '[name]__[local]___[hash:base64:5]'
        }
      },
      // 添加 NutUI React Taro 的 PostCSS 插件
      nutuiReactTaroPxToViewport: {
        enable: true,
        config: {
          viewportWidth: 375 // 设计稿宽度，根据实际情况调整
        }
      }
    }
  }
})
