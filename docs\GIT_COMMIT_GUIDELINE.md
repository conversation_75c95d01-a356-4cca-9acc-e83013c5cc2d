# Git 提交规范

## 提交格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

## Type 类型
- **feat**: 新功能（feature）
- **fix**: 修复bug
- **docs**: 文档更新
- **style**: 代码格式调整（不影响代码运行的变动）
- **refactor**: 重构（即不是新增功能，也不是修改bug的代码变动）
- **perf**: 性能优化
- **test**: 增加测试
- **chore**: 构建过程或辅助工具的变动
- **revert**: 回滚到上一个版本

## Scope 作用域
Scope 用于说明 commit 影响的范围，比如数据层、控制层、视图层等，视项目不同而不同。

例如在 Angular 项目中可以是：
- view
- router
- service
- directive
- component
- pipe
- module

## Subject 主题
Subject 是 commit 目的的简短描述，不超过 50 个字符。

- 以动词开头，使用第一人称现在时，比如 change，而不是 changed 或 changes
- 第一个字母小写
- 结尾不加句号（.）

## Body 正文
Body 是对本次 commit 的详细描述，可以分成多行。

- 应该说明代码变动的动机，以及与以前行为的对比

## Footer 脚注
Footer 只用于两种情况：

1. 不兼容变动
   ```
   BREAKING CHANGE: isolate scope bindings definition has changed and
   the inject option for the directive controller injection was removed.
   
   To migrate the code follow the example below:
   
   Before:
   
   scope: {
     foo: 'bar'
   }
   
   After:
   
   scope: {
     foo: '='
   }
   
   The removed `inject` wasn't generally useful for directives so there should be no code using it.
   ```

2. 关闭 Issue
   ```
   Closes #123, #245, #992
   ```

## 示例
```
feat(user): 添加用户登录功能

- 实现用户登录表单
- 添加登录验证逻辑
- 集成 JWT token 认证

Closes #123
```

```
fix(cart): 修复购物车商品数量计算错误

- 修正商品数量加减逻辑
- 修复价格计算精度问题

Fixes #456
```