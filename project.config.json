{"miniprogramRoot": "dist/", "projectname": "buyer-app", "description": "买方", "appid": "wx4b6ec811a25f257f", "setting": {"urlCheck": true, "es6": false, "enhance": false, "compileHotReLoad": false, "postcss": false, "minified": false, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "compileType": "miniprogram", "simulatorPluginLibVersion": {}, "packOptions": {"ignore": [], "include": []}, "editorSetting": {}}