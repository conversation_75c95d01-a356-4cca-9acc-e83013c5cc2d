export default {
  pages: [
    'pages/product-list/product-list',
    'pages/product-detail/product-detail',
    'pages/cart/cart',
    'pages/index/index'
  ],
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '水果商城',
    navigationBarTextStyle: 'black'
  },
  tabBar: {
    custom: false, // 禁用自定义 TabBar，使用原生 TabBar
    color: '#666',
    selectedColor: '#ff6b00',
    backgroundColor: '#fff',
    borderStyle: 'white',
    list: [
      {
        pagePath: 'pages/product-list/product-list',
        text: '商品',
        iconPath: 'assets/icons/liebiao.png',
        selectedIconPath: 'assets/icons/liebiao1.png'
      },
      {
        pagePath: 'pages/cart/cart',
        text: '购物车',
        iconPath: 'assets/icons/gouwuche.png',
        selectedIconPath: 'assets/icons/gouwuche1.png'
      },
      {
        pagePath: 'pages/index/index',
        text: '我的',
        iconPath: 'assets/icons/wode.png',
        selectedIconPath: 'assets/icons/wode1.png'
      }
    ]
  }
}
