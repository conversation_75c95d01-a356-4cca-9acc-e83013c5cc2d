# 图标使用说明

## 目录结构
- `src/assets/icons/` - 存放所有图标文件

## 使用方法
1. 将 SVG 或 PNG 格式的图标文件放入 `src/assets/icons/` 目录
2. 图标文件命名应简洁明了，例如：`shuiguo.svg`、`gouwuche.png`
3. 在需要使用图标的地方，引入 Icon 组件：
   ```jsx
   import Icon from '@/components/Icon/Icon'
   
   <Icon name="shuiguo" width={24} height={24} />
   ```

## 注意事项
1. 优先使用 SVG 格式的图标，因为它们在不同分辨率下都能保持清晰
2. 为了区分选中和未选中状态，可以为同一图标准备两个版本：
   - 默认状态：`icon-name.svg`
   - 选中状态：`icon-name-selected.svg`