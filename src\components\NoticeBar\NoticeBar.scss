/* NutUI NoticeBar 样式 - 基于官方源码 */
.nut-noticebar {
  width: 100%;

  .nut-noticebar-box {
    position: relative;
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 16px;
    font-size: 14px;
    background: #fefcec;
    color: #ed6a0c;
    border-radius: 0;

    &-wrapable,
    &-center {
      height: auto;
      padding: 9px 16px;

      .nut-noticebar-box-wrap {
        height: auto;
      }
    }

    &-wrapable {
      .nut-noticebar-box-wrap {
        .nut-noticebar-box-wrap-content {
          position: relative;
          white-space: normal;
          word-wrap: break-word;
        }
      }
    }

    &-center {
      justify-content: center;

      .nut-noticebar-box-wrap {
        flex: initial;

        .nut-noticebar-box-wrap-content {
          position: relative;
          display: initial;
        }
      }
    }

    &-left-icon {
      display: flex;
      height: 16px;
      min-width: 16px;
      margin-right: 8px;
      background-size: 100% 100%;
      align-items: center;
      justify-content: center;
    }

    &-right-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 16px;
      margin-left: 8px;
      cursor: pointer;
    }

    &-right {
      margin-left: 8px;
    }

    &-wrap {
      display: flex;
      flex: 1;
      align-items: center;
      height: 20px;
      line-height: 20px;
      overflow: hidden;
      position: relative;

      .nut-noticebar-box-wrap-content {
        position: absolute;
        white-space: nowrap;
        color: #ed6a0c;

        &.nut-ellipsis {
          max-width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          position: relative;
        }
      }
    }
  }
}

/* 水平滚动动画 */
.nut-notice-bar-play {
  animation: nut-notice-bar-play linear infinite;
}

@keyframes nut-notice-bar-play {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* 垂直滚动样式 */
.nut-noticebar-vertical {
  position: relative;
  height: 100%;
  overflow: hidden;

  &-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateY(20px);

    &-active {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

/* 主题变体 - 基于 NutUI 设计 */
.nut-noticebar {
  /* 成功主题 */
  &.theme-success {
    .nut-noticebar-box {
      background: #f0f9ff;
      color: #00796b;
    }
  }

  /* 警告主题 */
  &.theme-warning {
    .nut-noticebar-box {
      background: #fffbf0;
      color: #f57c00;
    }
  }

  /* 错误主题 */
  &.theme-error {
    .nut-noticebar-box {
      background: #fef2f2;
      color: #dc2626;
    }
  }

  /* 信息主题 */
  &.theme-info {
    .nut-noticebar-box {
      background: #f0f9ff;
      color: #2563eb;
    }
  }
}

/* 微信小程序兼容性优化 */
@media screen and (max-width: 750px) {
  .nut-noticebar {
    .nut-noticebar-box {
      font-size: 12px;
      padding: 0 12px;
    }
  }
}
