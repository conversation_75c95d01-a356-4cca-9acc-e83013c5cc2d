/* 自定义通知栏组件样式 */
.custom-notice-bar {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

.notice-content {
  display: flex;
  align-items: center;
  height: 100%;
  position: relative;
}

.notice-icon {
  margin-right: 10px;
  flex-shrink: 0;
  transition: transform 0.3s ease;

  .custom-notice-bar:hover & {
    transform: scale(1.1);
  }
}

.notice-text-container {
  flex: 1;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.notice-text {
  font-weight: 500;
  display: block;
  white-space: nowrap;
  transition: all 0.3s ease;

  /* 多条消息切换动画 */
  &.multiple {
    animation: slideInUp 0.5s ease-in-out;
  }

  /* 单条长消息滚动动画 */
  &.scrollable {
    animation: scrollText 10s linear infinite;
  }
}

.notice-close {
  color: #999;
  margin-left: 10px;
  padding: 5px;
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 50%;

  &:hover {
    color: #666;
    background-color: rgba(0, 0, 0, 0.05);
  }

  &:active {
    transform: scale(0.95);
  }
}

/* 滑入动画 */
@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 横向滚动动画 */
@keyframes scrollText {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* 微信小程序兼容性优化 */
@media screen and (max-width: 750px) {
  .notice-text {
    font-size: 24px !important;
  }
  
  .notice-icon {
    font-size: 26px !important;
  }
  
  .notice-close {
    font-size: 22px !important;
  }
}

/* 主题变体 */
.custom-notice-bar {
  /* 成功主题 */
  &.theme-success {
    background-color: #f0f9ff;
    border-color: #e1f5fe;
    
    .notice-text {
      color: #00796b;
    }
    
    .notice-icon {
      color: #00796b;
    }
  }

  /* 警告主题 */
  &.theme-warning {
    background-color: #fffbf0;
    border-color: #fff3cd;
    
    .notice-text {
      color: #f57c00;
    }
    
    .notice-icon {
      color: #f57c00;
    }
  }

  /* 错误主题 */
  &.theme-error {
    background-color: #fef2f2;
    border-color: #fecaca;
    
    .notice-text {
      color: #dc2626;
    }
    
    .notice-icon {
      color: #dc2626;
    }
  }

  /* 信息主题 */
  &.theme-info {
    background-color: #f0f9ff;
    border-color: #bfdbfe;
    
    .notice-text {
      color: #2563eb;
    }
    
    .notice-icon {
      color: #2563eb;
    }
  }
}

/* 尺寸变体 */
.custom-notice-bar {
  /* 小尺寸 */
  &.size-small {
    padding: 8px 16px;
    
    .notice-text {
      font-size: 22px;
    }
    
    .notice-icon {
      font-size: 24px;
    }
  }

  /* 大尺寸 */
  &.size-large {
    padding: 16px 24px;
    
    .notice-text {
      font-size: 30px;
    }
    
    .notice-icon {
      font-size: 32px;
    }
  }
}

/* 无边框样式 */
.custom-notice-bar.no-border {
  border: none;
  box-shadow: none;
}

/* 圆角样式 */
.custom-notice-bar.rounded {
  border-radius: 8px;
}

/* 阴影样式 */
.custom-notice-bar.shadow {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
