import React, { useState, useEffect, useRef, useCallback } from 'react'
import { View, Text } from '@tarojs/components'
import classNames from 'classnames'
import './NoticeBar.scss'

export type NoticeBarAlign = 'left' | 'center'
export type NoticeBarDirection = 'horizontal' | 'vertical'

export interface NoticeBarProps {
  /** 通知内容 */
  content?: string
  /** 通知列表，用于垂直滚动 */
  list?: string[]
  /** 滚动方向 */
  direction?: NoticeBarDirection
  /** 文字对齐方式 */
  align?: NoticeBarAlign
  /** 高度 */
  height?: number
  /** 是否可关闭 */
  closeable?: boolean
  /** 是否换行 */
  wrap?: boolean
  /** 左侧图标 */
  leftIcon?: React.ReactNode
  /** 右侧图标 */
  rightIcon?: React.ReactNode
  /** 右侧内容 */
  right?: React.ReactNode
  /** 是否可滚动，null时自动判断 */
  scrollable?: boolean | null
  /** 滚动速度 */
  speed?: number
  /** 垂直滚动时的停留时间 */
  duration?: number
  /** 延迟时间 */
  delay?: number
  /** 自定义类名 */
  className?: string
  /** 自定义样式 */
  style?: React.CSSProperties
  /** 点击事件 */
  onClick?: (event: any) => void
  /** 关闭事件 */
  onClose?: (event: any) => void
  /** 垂直滚动时的项目点击事件 */
  onItemClick?: (event: any, item: string, index: number) => void
}

// 默认属性，模仿 NutUI 的设计
const defaultProps: Partial<NoticeBarProps> = {
  align: 'left',
  direction: 'horizontal',
  list: [],
  duration: 1000,
  height: 40,
  content: '',
  closeable: false,
  wrap: false,
  leftIcon: <Text style={{ fontSize: '16px' }}>📢</Text>,
  rightIcon: null,
  right: null,
  delay: 1,
  scrollable: null,
  speed: 50
}

const NoticeBar: React.FC<NoticeBarProps> = (props) => {
  const {
    className,
    style,
    align = 'left',
    direction = 'horizontal',
    list = [],
    duration = 1000,
    height = 40,
    content = '',
    closeable = false,
    wrap = false,
    leftIcon = <Text style={{ fontSize: '16px' }}>📢</Text>,
    rightIcon = null,
    right = null,
    delay = 1,
    scrollable = null,
    speed = 50,
    onClick,
    onClose,
    onItemClick
  } = { ...defaultProps, ...props }

  const classPrefix = 'nut-noticebar'
  const wrapRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)

  const [showNoticeBar, setShowNoticeBar] = useState(true)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [animationClass, setAnimationClass] = useState('')
  const [isCanScroll, setIsCanScroll] = useState<boolean | null>(null)
  const [animationDuration, setAnimationDuration] = useState(0)

  const isVertical = direction === 'vertical'
  const scrollList = useRef<string[]>([])
  const timer = useRef<NodeJS.Timeout | null>(null)

  // 初始化滚动列表
  useEffect(() => {
    if (isVertical) {
      scrollList.current = list.length > 0 ? [...list] : []
      if (scrollList.current.length > 0) {
        startVerticalRoll()
      }
    } else {
      initHorizontalScroll()
    }

    return () => {
      if (timer.current) {
        clearTimeout(timer.current)
      }
    }
  }, [list, content, isVertical])

  // 垂直滚动逻辑
  const startVerticalRoll = useCallback(() => {
    if (scrollList.current.length <= 1) return

    const roll = () => {
      setCurrentIndex((prevIndex) => {
        const newIndex = (prevIndex + 1) % scrollList.current.length
        return newIndex
      })

      timer.current = setTimeout(roll, duration)
    }

    timer.current = setTimeout(roll, duration)
  }, [duration])

  // 水平滚动逻辑
  const initHorizontalScroll = useCallback(() => {
    if (!content) return

    // 模拟检测是否需要滚动
    const shouldScroll = scrollable === null ? content.length > 20 : scrollable
    setIsCanScroll(shouldScroll)

    if (shouldScroll) {
      const duration = (content.length * 1000) / speed
      setAnimationDuration(duration)
      setAnimationClass('nut-notice-bar-play')
    }
  }, [content, scrollable, speed])

  // 处理关闭
  const handleClose = useCallback((event: any) => {
    event.stopPropagation()
    setShowNoticeBar(false)
    onClose?.(event)
  }, [onClose])

  // 处理点击
  const handleClick = useCallback((event: any) => {
    onClick?.(event)
  }, [onClick])

  // 处理垂直滚动项目点击
  const handleItemClick = useCallback((event: any, item: string, index: number) => {
    onItemClick?.(event, item, index)
  }, [onItemClick])

  if (!showNoticeBar) {
    return null
  }

  const classes = classNames(
    classPrefix,
    className
  )

  const boxClasses = classNames(
    `${classPrefix}-box`,
    {
      [`${classPrefix}-box-wrapable`]: wrap,
      [`${classPrefix}-box-center`]: align === 'center'
    }
  )

  const wrapClasses = classNames(
    `${classPrefix}-box-wrap`
  )

  const contentClasses = classNames(
    `${classPrefix}-box-wrap-content`,
    animationClass,
    {
      'nut-ellipsis': !isCanScroll && !wrap
    }
  )

  const renderHorizontalContent = () => (
    <View className={wrapClasses} ref={wrapRef}>
      <View
        className={contentClasses}
        ref={contentRef}
        style={{
          animationDuration: isCanScroll ? `${animationDuration}ms` : undefined,
          transform: isCanScroll ? 'translateX(100%)' : undefined
        }}
      >
        {content}
      </View>
    </View>
  )

  const renderVerticalContent = () => (
    <View className={`${classPrefix}-vertical`}>
      {scrollList.current.map((item, index) => (
        <View
          key={index}
          className={classNames(
            `${classPrefix}-vertical-item`,
            {
              [`${classPrefix}-vertical-item-active`]: index === currentIndex
            }
          )}
          style={{
            height: `${height}px`,
            lineHeight: `${height}px`,
            display: index === currentIndex ? 'block' : 'none'
          }}
          onClick={(e) => handleItemClick(e, item, index)}
        >
          {item}
        </View>
      ))}
    </View>
  )

  return (
    <View className={classes} style={style}>
      <View className={boxClasses} style={{ height: `${height}px` }} onClick={handleClick}>
        {/* 左侧图标 */}
        {leftIcon && (
          <View className={`${classPrefix}-box-left-icon`}>
            {leftIcon}
          </View>
        )}

        {/* 内容区域 */}
        {isVertical ? renderVerticalContent() : renderHorizontalContent()}

        {/* 右侧内容 */}
        {right && (
          <View className={`${classPrefix}-box-right`}>
            {right}
          </View>
        )}

        {/* 右侧图标 */}
        {rightIcon && (
          <View className={`${classPrefix}-box-right-icon`}>
            {rightIcon}
          </View>
        )}

        {/* 关闭按钮 */}
        {closeable && (
          <View
            className={`${classPrefix}-box-right-icon`}
            onClick={handleClose}
          >
            <Text style={{ fontSize: '12px' }}>✕</Text>
          </View>
        )}
      </View>
    </View>
  )
}

export default NoticeBar
