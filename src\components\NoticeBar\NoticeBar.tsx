import React, { useState, useEffect } from 'react'
import { View, Text } from '@tarojs/components'
import './NoticeBar.scss'

export interface NoticeBarProps {
  /** 通知内容，支持字符串或字符串数组 */
  content?: string | string[]
  /** 是否显示左侧图标 */
  leftIcon?: boolean
  /** 左侧图标内容 */
  leftIconContent?: string
  /** 是否可关闭 */
  closeable?: boolean
  /** 关闭图标内容 */
  closeIconContent?: string
  /** 是否可滚动（当内容超出容器宽度时） */
  scrollable?: boolean
  /** 自动切换间隔时间（毫秒），仅在content为数组时生效 */
  duration?: number
  /** 滚动速度（像素/秒），仅在scrollable为true时生效 */
  speed?: number
  /** 背景颜色 */
  backgroundColor?: string
  /** 文字颜色 */
  textColor?: string
  /** 左侧图标颜色 */
  iconColor?: string
  /** 字体大小 */
  fontSize?: number
  /** 高度 */
  height?: number
  /** 是否显示边框 */
  border?: boolean
  /** 边框颜色 */
  borderColor?: string
  /** 圆角大小 */
  borderRadius?: number
  /** 内边距 */
  padding?: string
  /** 外边距 */
  margin?: string
  /** 自定义类名 */
  className?: string
  /** 自定义样式 */
  style?: React.CSSProperties
  /** 关闭回调 */
  onClose?: () => void
  /** 点击回调 */
  onClick?: (content: string, index?: number) => void
  /** 切换回调（仅在多条消息时触发） */
  onChange?: (content: string, index: number) => void
}

const NoticeBar: React.FC<NoticeBarProps> = ({
  content = '',
  leftIcon = true,
  leftIconContent = '📢',
  closeable = false,
  closeIconContent = '✕',
  scrollable = false,
  duration = 3000,
  speed = 50,
  backgroundColor = '#fff',
  textColor = '#ff6b00',
  iconColor = '#ff6b00',
  fontSize = 26,
  height = 40,
  border = true,
  borderColor = '#f0f0f0',
  borderRadius = 0,
  padding = '12px 20px',
  margin = '0',
  className = '',
  style = {},
  onClose,
  onClick,
  onChange
}) => {
  const [visible, setVisible] = useState(true)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isScrolling, setIsScrolling] = useState(false)

  // 处理内容数组
  const contentArray = Array.isArray(content) ? content : [content]
  const isMultiple = contentArray.length > 1
  const currentContent = contentArray[currentIndex] || ''

  // 自动切换逻辑（多条消息时）
  useEffect(() => {
    if (!visible || !isMultiple || duration <= 0) return

    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => {
        const newIndex = (prevIndex + 1) % contentArray.length
        const newContent = contentArray[newIndex]
        onChange?.(newContent, newIndex)
        return newIndex
      })
    }, duration)

    return () => clearInterval(timer)
  }, [visible, isMultiple, duration, contentArray.length, onChange])

  // 滚动逻辑（单条长消息时）
  useEffect(() => {
    if (!visible || !scrollable || isMultiple) return

    // 这里可以添加横向滚动逻辑
    // 由于微信小程序的限制，暂时使用CSS动画实现
    setIsScrolling(true)
  }, [visible, scrollable, isMultiple, currentContent])

  // 处理关闭
  const handleClose = () => {
    setVisible(false)
    onClose?.()
  }

  // 处理点击
  const handleClick = () => {
    onClick?.(currentContent, isMultiple ? currentIndex : undefined)
  }

  if (!visible || !currentContent) {
    return null
  }

  const containerStyle: React.CSSProperties = {
    backgroundColor,
    padding,
    margin,
    height: `${height}px`,
    borderTop: border ? `1px solid ${borderColor}` : 'none',
    borderRadius: `${borderRadius}px`,
    ...style
  }

  const textStyle: React.CSSProperties = {
    color: textColor,
    fontSize: `${fontSize}px`,
    lineHeight: `${height}px`
  }

  const iconStyle: React.CSSProperties = {
    color: iconColor,
    fontSize: `${fontSize + 2}px`
  }

  return (
    <View 
      className={`custom-notice-bar ${className}`} 
      style={containerStyle}
      onClick={handleClick}
    >
      <View className='notice-content'>
        {leftIcon && (
          <Text className='notice-icon' style={iconStyle}>
            {leftIconContent}
          </Text>
        )}
        
        <View className='notice-text-container'>
          <Text 
            className={`notice-text ${isMultiple ? 'multiple' : ''} ${scrollable && !isMultiple ? 'scrollable' : ''}`}
            style={textStyle}
            key={isMultiple ? currentIndex : 'single'}
          >
            {currentContent}
          </Text>
        </View>

        {closeable && (
          <Text 
            className='notice-close' 
            style={{ fontSize: `${fontSize - 2}px` }}
            onClick={(e) => {
              e.stopPropagation()
              handleClose()
            }}
          >
            {closeIconContent}
          </Text>
        )}
      </View>
    </View>
  )
}

export default NoticeBar
