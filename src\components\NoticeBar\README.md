# NoticeBar 通知栏组件

一个灵活可配置的通知栏组件，支持单条消息滚动和多条消息轮播，完全兼容微信小程序。

## 特性

- 🎯 **多种内容模式**：支持单条消息和多条消息轮播
- 🎨 **高度可定制**：支持自定义颜色、字体、尺寸等样式
- 📱 **微信小程序适配**：完全兼容微信小程序环境
- ⚡ **性能优化**：使用 CSS 动画，流畅的切换效果
- 🔧 **TypeScript 支持**：完整的类型定义

## 基础用法

### 单条消息

```tsx
import NoticeBar from '@/components/NoticeBar'

// 基础用法
<NoticeBar content="这是一条通知消息" />

// 可关闭的通知
<NoticeBar 
  content="这是一条可关闭的通知消息" 
  closeable 
  onClose={() => console.log('通知已关闭')}
/>

// 可滚动的长消息
<NoticeBar 
  content="这是一条很长很长的通知消息，当内容超出容器宽度时会自动滚动显示" 
  scrollable 
/>
```

### 多条消息轮播

```tsx
// 多条消息自动轮播
<NoticeBar 
  content={[
    '🎉 新品上市：新疆红富士苹果，脆甜多汁，营养丰富！',
    '🔥 限时特惠：赣南脐橙，买一送一，仅限今日！',
    '✨ 品质保证：所有水果均来自优质产地，新鲜直达！',
    '🚚 免费配送：满99元免费送货上门，快速到达！'
  ]}
  duration={3000}
  onChange={(content, index) => {
    console.log('切换到第', index + 1, '条消息:', content)
  }}
/>
```

## 样式定制

### 颜色和字体

```tsx
<NoticeBar 
  content="自定义样式的通知栏"
  backgroundColor="#f0f9ff"
  textColor="#2563eb"
  iconColor="#2563eb"
  fontSize={28}
  height={50}
/>
```

### 边框和圆角

```tsx
<NoticeBar 
  content="带圆角和边框的通知栏"
  border={true}
  borderColor="#e1f5fe"
  borderRadius={8}
  className="rounded shadow"
/>
```

### 主题样式

```tsx
// 成功主题
<NoticeBar content="操作成功！" className="theme-success" />

// 警告主题
<NoticeBar content="请注意！" className="theme-warning" />

// 错误主题
<NoticeBar content="操作失败！" className="theme-error" />

// 信息主题
<NoticeBar content="温馨提示" className="theme-info" />
```

### 尺寸变体

```tsx
// 小尺寸
<NoticeBar content="小尺寸通知栏" className="size-small" />

// 默认尺寸
<NoticeBar content="默认尺寸通知栏" />

// 大尺寸
<NoticeBar content="大尺寸通知栏" className="size-large" />
```

## 事件处理

```tsx
<NoticeBar 
  content={['消息1', '消息2', '消息3']}
  closeable
  onClick={(content, index) => {
    console.log('点击了通知栏:', content, index)
  }}
  onChange={(content, index) => {
    console.log('消息切换:', content, index)
  }}
  onClose={() => {
    console.log('通知栏已关闭')
  }}
/>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| content | 通知内容，支持字符串或字符串数组 | `string \| string[]` | `''` |
| leftIcon | 是否显示左侧图标 | `boolean` | `true` |
| leftIconContent | 左侧图标内容 | `string` | `'📢'` |
| closeable | 是否可关闭 | `boolean` | `false` |
| closeIconContent | 关闭图标内容 | `string` | `'✕'` |
| scrollable | 是否可滚动（单条长消息时） | `boolean` | `false` |
| duration | 自动切换间隔时间（毫秒） | `number` | `3000` |
| speed | 滚动速度（像素/秒） | `number` | `50` |
| backgroundColor | 背景颜色 | `string` | `'#fff'` |
| textColor | 文字颜色 | `string` | `'#ff6b00'` |
| iconColor | 图标颜色 | `string` | `'#ff6b00'` |
| fontSize | 字体大小 | `number` | `26` |
| height | 高度 | `number` | `40` |
| border | 是否显示边框 | `boolean` | `true` |
| borderColor | 边框颜色 | `string` | `'#f0f0f0'` |
| borderRadius | 圆角大小 | `number` | `0` |
| padding | 内边距 | `string` | `'12px 20px'` |
| margin | 外边距 | `string` | `'0'` |
| className | 自定义类名 | `string` | `''` |
| style | 自定义样式 | `React.CSSProperties` | `{}` |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| onClose | 关闭时触发 | `() => void` |
| onClick | 点击时触发 | `(content: string, index?: number) => void` |
| onChange | 消息切换时触发（多条消息时） | `(content: string, index: number) => void` |

## 样式类名

### 主题类名
- `theme-success` - 成功主题
- `theme-warning` - 警告主题  
- `theme-error` - 错误主题
- `theme-info` - 信息主题

### 尺寸类名
- `size-small` - 小尺寸
- `size-large` - 大尺寸

### 其他类名
- `no-border` - 无边框
- `rounded` - 圆角
- `shadow` - 阴影

## 注意事项

1. 当 `content` 为数组时，组件会自动进入多条消息轮播模式
2. `scrollable` 属性仅在单条消息模式下生效
3. 在微信小程序中，建议使用 `rpx` 单位来适配不同屏幕尺寸
4. 组件已针对微信小程序环境进行优化，确保良好的性能表现
