# NoticeBar 通知栏组件

基于 NutUI 官方设计的通知栏组件，完全兼容微信小程序，支持水平滚动和垂直轮播。

## 特性

- 🎯 **NutUI 兼容**：完全基于 NutUI 官方 API 设计
- 🔄 **双向滚动**：支持水平滚动和垂直轮播
- 📱 **小程序优化**：针对微信小程序环境优化
- ⚡ **高性能**：使用原生 CSS 动画
- 🔧 **TypeScript**：完整的类型定义

## 基础用法

### 水平滚动（单条消息）

```tsx
import NoticeBar from '@/components/NoticeBar'

// 基础用法
<NoticeBar content="这是一条通知消息" />

// 可关闭的通知
<NoticeBar
  content="这是一条可关闭的通知消息"
  closeable
  onClose={(event) => console.log('通知已关闭')}
/>

// 强制滚动的长消息
<NoticeBar
  content="这是一条很长很长的通知消息，当内容超出容器宽度时会自动滚动显示"
  scrollable={true}
  speed={50}
/>
```

### 垂直轮播（多条消息）

```tsx
// 垂直轮播多条消息
<NoticeBar
  direction="vertical"
  list={[
    '🎉 新品上市：新疆红富士苹果，脆甜多汁，营养丰富！',
    '🔥 限时特惠：赣南脐橙，买一送一，仅限今日！',
    '✨ 品质保证：所有水果均来自优质产地，新鲜直达！',
    '🚚 免费配送：满99元免费送货上门，快速到达！'
  ]}
  duration={3000}
  onItemClick={(event, item, index) => {
    console.log('点击了第', index + 1, '条消息:', item)
  }}
/>
```

## 样式定制

### 颜色和字体

```tsx
<NoticeBar 
  content="自定义样式的通知栏"
  backgroundColor="#f0f9ff"
  textColor="#2563eb"
  iconColor="#2563eb"
  fontSize={28}
  height={50}
/>
```

### 边框和圆角

```tsx
<NoticeBar 
  content="带圆角和边框的通知栏"
  border={true}
  borderColor="#e1f5fe"
  borderRadius={8}
  className="rounded shadow"
/>
```

### 主题样式

```tsx
// 成功主题
<NoticeBar content="操作成功！" className="theme-success" />

// 警告主题
<NoticeBar content="请注意！" className="theme-warning" />

// 错误主题
<NoticeBar content="操作失败！" className="theme-error" />

// 信息主题
<NoticeBar content="温馨提示" className="theme-info" />
```

### 尺寸变体

```tsx
// 小尺寸
<NoticeBar content="小尺寸通知栏" className="size-small" />

// 默认尺寸
<NoticeBar content="默认尺寸通知栏" />

// 大尺寸
<NoticeBar content="大尺寸通知栏" className="size-large" />
```

## 事件处理

```tsx
<NoticeBar 
  content={['消息1', '消息2', '消息3']}
  closeable
  onClick={(content, index) => {
    console.log('点击了通知栏:', content, index)
  }}
  onChange={(content, index) => {
    console.log('消息切换:', content, index)
  }}
  onClose={() => {
    console.log('通知栏已关闭')
  }}
/>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| content | 通知内容（水平滚动时使用） | `string` | `''` |
| list | 通知列表（垂直轮播时使用） | `string[]` | `[]` |
| direction | 滚动方向 | `'horizontal' \| 'vertical'` | `'horizontal'` |
| align | 文字对齐方式 | `'left' \| 'center'` | `'left'` |
| height | 高度 | `number` | `40` |
| closeable | 是否可关闭 | `boolean` | `false` |
| wrap | 是否换行 | `boolean` | `false` |
| leftIcon | 左侧图标 | `React.ReactNode` | `📢` |
| rightIcon | 右侧图标 | `React.ReactNode` | `null` |
| right | 右侧内容 | `React.ReactNode` | `null` |
| scrollable | 是否可滚动，null时自动判断 | `boolean \| null` | `null` |
| speed | 滚动速度 | `number` | `50` |
| duration | 垂直滚动时的停留时间 | `number` | `1000` |
| delay | 延迟时间 | `number` | `1` |
| className | 自定义类名 | `string` | `''` |
| style | 自定义样式 | `React.CSSProperties` | `{}` |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| onClick | 点击通知栏时触发 | `(event: any) => void` |
| onClose | 关闭时触发 | `(event: any) => void` |
| onItemClick | 垂直滚动时点击项目触发 | `(event: any, item: string, index: number) => void` |

## 样式类名

### 主题类名
- `theme-success` - 成功主题
- `theme-warning` - 警告主题  
- `theme-error` - 错误主题
- `theme-info` - 信息主题

### 尺寸类名
- `size-small` - 小尺寸
- `size-large` - 大尺寸

### 其他类名
- `no-border` - 无边框
- `rounded` - 圆角
- `shadow` - 阴影

## 注意事项

1. 当 `content` 为数组时，组件会自动进入多条消息轮播模式
2. `scrollable` 属性仅在单条消息模式下生效
3. 在微信小程序中，建议使用 `rpx` 单位来适配不同屏幕尺寸
4. 组件已针对微信小程序环境进行优化，确保良好的性能表现
