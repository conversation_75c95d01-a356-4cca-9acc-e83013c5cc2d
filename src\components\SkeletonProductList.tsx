import { View } from '@tarojs/components'
import { Skeleton } from '@nutui/nutui-react-taro'
import '@nutui/nutui-react-taro/dist/style.css'
import './SkeletonProductList.scss'

const SkeletonProductList = () => {
  return (
    <View className='skeleton-product-list'>
      {Array.from({ length: 6 }).map((_, index) => (
        <View key={index} className='skeleton-product-item'>
          <Skeleton width='140px' height='140px' round />
          <View className='skeleton-product-info'>
            <Skeleton width='60%' height='20px' />
            <Skeleton width='40%' height='16px' />
            <Skeleton width='80%' height='16px' />
            <View className='skeleton-price-section'>
              <Skeleton width='30%' height='24px' />
              <Skeleton width='20%' height='14px' style={{ marginLeft: '10px' }} />
            </View>
          </View>
        </View>
      ))}
    </View>
  )
}

export default SkeletonProductList