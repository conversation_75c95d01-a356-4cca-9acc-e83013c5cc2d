.cart {
  background-color: #f5f5f5;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.cart-header {
  background-color: #fff;
  padding: 30px 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

.cart-title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
}

.select-all {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 30px 20px;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.select-all-checkbox {
  font-size: 28px;
  color: #333;
}

.cart-items {
  flex: 1;
  background-color: #fff;
  margin-bottom: 20px;
  overflow-y: auto;
}

.cart-item {
  display: flex;
  align-items: center;
  padding: 30px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.item-checkbox {
  margin-right: 20px;
}

.item-image {
  width: 120px;
  height: 120px;
  border-radius: 12px;
  margin: 0 20px;
}

.item-info {
  flex: 1;
}

.item-name {
  font-size: 28px;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.item-price {
  font-size: 26px;
  color: #ff6b00;
  margin-bottom: 20px;
}

.quantity-control {
  display: flex;
  align-items: center;
}

.item-subtotal {
  font-size: 28px;
  color: #ff6b00;
  font-weight: bold;
  min-width: 120px;
  text-align: right;
}

.delivery-info {
  background-color: #fff;
  padding: 30px 20px;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.delivery-info Text {
  display: block;
  font-size: 26px;
  color: #666;
  margin-bottom: 10px;
}

.checkout-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100px;
  background-color: #fff;
  padding: 0 20px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.total-section {
  display: flex;
  align-items: center;
}

.total-label {
  font-size: 28px;
  color: #333;
  margin-right: 10px;
}

.total-amount {
  font-size: 36px;
  color: #ff6b00;
  font-weight: bold;
}

.checkout-button {
  background-color: #ff6b00;
  color: #fff;
  padding: 0 40px;
  height: 70px;
  border-radius: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  font-weight: bold;
  transition: background-color 0.3s ease, transform 0.1s ease; /* 添加过渡效果 */
}

.checkout-button:active {
  transform: scale(0.98); /* 点击时轻微缩小 */
}

/* Recommend section styles */
.recommend-section {
  background-color: #fff;
  padding: 20px;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.section-title {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
  display: block;
}

.nut-swiper {
  border-radius: 12px;
  overflow: hidden;
}

/* Custom Swiper styles */
.custom-swiper .nut-swiper-item {
  padding: 0 10px; /* 增加左右内边距，使卡片不贴边 */
  box-sizing: border-box;
}

.swiper-item-content {
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* 增加阴影 */
  transition: transform 0.3s ease, box-shadow 0.3s ease; /* 添加过渡效果 */
  animation: fadeIn 0.5s ease-in-out; /* 添加淡入动画 */
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.swiper-item-content:hover {
  transform: translateY(-5px); /* 悬停时轻微上移 */
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15); /* 悬停时增强阴影 */
}

/* 入场动画 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 指示点样式微调 */
.nut-swiper-pagination {
  bottom: 10px; /* 调整指示点位置 */
}

/* 推荐卡片样式 */
.recommend-card {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
}

.recommend-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.recommend-info {
  padding: 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.recommend-title {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
  line-height: 1.4;
}

.recommend-price {
  font-size: 26px;
  color: #ff6b00;
  font-weight: bold;
}