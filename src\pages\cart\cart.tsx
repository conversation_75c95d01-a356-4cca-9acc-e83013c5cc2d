import { useState } from 'react' // 引入 useState
import { View, Text, Image } from '@tarojs/components'
import Taro, { useLoad, useDidShow } from '@tarojs/taro' // 引入 Taro 和 useDidShow
import { Checkbox, Stepper } from '@taroify/core'
import { Card, Button, Swiper, SwiperItem } from '@nutui/nutui-react-taro'
import '@taroify/core/index.scss'
import '@nutui/nutui-react-taro/dist/style.css'
import './cart.scss'

interface CartItem {
  id: number
  productId: number
  name: string
  price: number
  unit: string
  image: string
  quantity: number
  subtotal: number
}

export default function Cart() {
  useLoad(() => {
    console.log('Cart page loaded.')
  })

  useDidShow(() => {
    // 页面显示时，通知自定义 TabBar 更新选中项为索引 1 ('购物车' tab)
    Taro.eventCenter.trigger('TARO_TABBAR_CHANGE', 1)
  })

  const cartItems: CartItem[] = [
    {
      id: 1,
      productId: 1,
      name: '新疆红富士苹果',
      price: 12.8,
      unit: '斤',
      image: 'https://example.com/apple.jpg',
      quantity: 2,
      subtotal: 25.6
    },
    {
      id: 2,
      productId: 2,
      name: '赣南脐橙',
      price: 8.8,
      unit: '斤',
      image: 'https://example.com/orange.jpg',
      quantity: 1,
      subtotal: 8.8
    },
    {
      id: 3,
      productId: 3,
      name: '海南芒果',
      price: 15.8,
      unit: '斤',
      image: 'https://example.com/mango.jpg',
      quantity: 1,
      subtotal: 15.8
    }
  ]

  const selectedItems = [1, 2, 3]
  const selectAll = true

  const handleSelectItem = (id: number) => {
    console.log('Item selected:', id)
  }

  const handleSelectAll = () => {
    console.log('Select all clicked')
  }

  const handleQuantityChange = (id: number, value: number) => {
    console.log('Quantity changed:', id, value)
  }

  const calculateTotal = () => {
    return cartItems
      .filter(item => selectedItems.includes(item.id))
      .reduce((total, item) => total + item.subtotal, 0)
  }

  const total = calculateTotal()
  
  // 新增状态：控制 Swiper 当前显示的索引
  const [currentSwiperIndex, setCurrentSwiperIndex] = useState(0)

  return (
    <View className='cart'>
      <View className='cart-header'>
        <Text className='cart-title'>🛒 购物车 ({cartItems.length}件商品)</Text>
      </View>

      <View className='select-all'>
        <Checkbox 
          className='select-all-checkbox' 
          checked={selectAll} 
          onChange={handleSelectAll}
        >
          全选
        </Checkbox>
      </View>

      <View className='cart-items'>
        {cartItems.map(item => (
          <View key={item.id} className='cart-item'>
            <Checkbox 
              className='item-checkbox' 
              checked={selectedItems.includes(item.id)} 
              onChange={() => handleSelectItem(item.id)}
            />
            
            <Image className='item-image' src={item.image} mode='aspectFill' />
            
            <View className='item-info'>
              <Text className='item-name'>{item.name}</Text>
              <Text className='item-price'>¥{item.price}/{item.unit}</Text>
              <View className='quantity-control'>
                <Stepper 
                  value={item.quantity} 
                  onChange={(value) => handleQuantityChange(item.id, value as number)} 
                  min={1}
                />
              </View>
            </View>
            
            <Text className='item-subtotal'>¥{item.subtotal.toFixed(1)}</Text>
          </View>
        ))}
      </View>

      {/* 推荐商品 */}
      <View className='recommend-section'>
        <Text className='section-title'>为您推荐</Text>
        <Swiper
          className='custom-swiper' // 添加自定义类名
          height={200}
          autoplay={{
            delay: 3000, // 设置自动播放延迟为3秒
            disableOnInteraction: false, // 用户操作后不禁用自动播放
          }}
          loop
          indicator // 显示指示点
          indicatorColor='#999' // 指示点颜色
          indicatorActiveColor='#ff6b00' // 当前指示点颜色
          onChange={(e) => setCurrentSwiperIndex(e.detail.current)} // 监听索引变化
        >
          <SwiperItem>
            <View className='swiper-item-content' style={{ animation: currentSwiperIndex === 0 ? 'slideInUp 0.5s ease-out' : 'none' }}>
              <Card
                src="https://example.com/recommend1.jpg"
                title="新鲜草莓"
                price="¥29.9/斤"
              />
            </View>
          </SwiperItem>
          <SwiperItem>
            <View className='swiper-item-content' style={{ animation: currentSwiperIndex === 1 ? 'slideInUp 0.5s ease-out' : 'none' }}>
              <Card
                src="https://example.com/recommend2.jpg"
                title="进口车厘子"
                price="¥89.9/斤"
              />
            </View>
          </SwiperItem>
          <SwiperItem>
            <View className='swiper-item-content' style={{ animation: currentSwiperIndex === 2 ? 'slideInUp 0.5s ease-out' : 'none' }}>
              <Card
                src="https://example.com/recommend3.jpg"
                title="海南椰子"
                price="¥15.0/个"
              />
            </View>
          </SwiperItem>
        </Swiper>
      </View>

      <View className='delivery-info'>
        <Text>💡 配送说明</Text>
        <Text>🚚 30分钟内送达 | 配送费¥3</Text>
      </View>

      <View className='checkout-bar'>
        <View className='total-section'>
          <Text className='total-label'>合计:</Text>
          <Text className='total-amount'>¥{total.toFixed(1)}</Text>
        </View>
        <View className='checkout-button'>
          <Text>去结算 ({selectedItems.length}件)</Text>
        </View>
      </View>
    </View>
  )
}