.profile {
  background-color: #f5f5f5;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  background-color: #fff;
  padding: 40px 30px;
  margin: 20px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

.user-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.user-details {
  flex: 1;
  margin: 0 30px;
}

.username {
  font-size: 36px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.user-id {
  font-size: 28px;
  color: #999;
}

.menu-section {
  background-color: #fff;
  border-radius: 16px;
  margin: 0 20px 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  flex: 1;
  overflow-y: auto;
}

.menu-item-left {
  display: flex;
  align-items: center;
}

.menu-text {
  font-size: 30px;
  color: #333;
  margin-left: 20px;
}

.logout-section {
  text-align: center;
  margin: 20px;
  flex-shrink: 0;
}

/* NutUI styles override */
.nut-cell {
  padding: 30px 30px;
  border-bottom: 1px solid #f0f0f0;
}

.nut-cell:last-child {
  border-bottom: none;
}

.nut-noticebar {
  margin-top: 20px;
}

/* Ripple effect styles */
.ripple-container {
  position: relative;
  overflow: hidden; /* 隐藏溢出的波纹 */
  border-radius: 16px; /* 与 Cell 的圆角保持一致 */
}

.ripple-active::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: ripple 0.6s linear;
  pointer-events: none; /* 防止波纹阻挡点击 */
}

@keyframes ripple {
  to {
    width: 400px;
    height: 400px;
    opacity: 0;
  }
}