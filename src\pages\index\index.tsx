import { useState } from 'react' // 引入 useState
import { View, Text } from '@tarojs/components'
import Taro, { useLoad, useDidShow } from '@tarojs/taro' // 引入 Taro 和 useDidShow
import { Cell, Button, Avatar, NoticeBar } from '@nutui/nutui-react-taro'
import { User, Location, Coupon, Service, Setting, ArrowRight } from '@nutui/icons-react-taro'
import '@nutui/nutui-react-taro/dist/style.css'
import './index.scss'

// 定义菜单项接口
interface MenuItem {
  icon: React.ReactNode
  text: string
  arrow: React.ReactNode
}

export default function Profile() {
  useLoad(() => {
    console.log('Profile page loaded.')
  })

  useDidShow(() => {
    // 页面显示时，通知自定义 TabBar 更新选中项为索引 2 ('我的' tab)
    Taro.eventCenter.trigger('TARO_TABBAR_CHANGE', 2)
  })

  const menuItems: MenuItem[] = [
    { icon: <User />, text: '我的订单', arrow: <ArrowRight /> },
    { icon: <Location />, text: '收货地址', arrow: <ArrowRight /> },
    { icon: <Coupon />, text: '优惠券', arrow: <ArrowRight /> },
    { icon: <Service />, text: '客服中心', arrow: <ArrowRight /> },
    { icon: <Setting />, text: '设置', arrow: <ArrowRight /> }
  ]

  // 新增状态：记录被点击的菜单项索引
  const [activeIndex, setActiveIndex] = useState<number | null>(null)

  // 处理菜单项点击事件
  const handleMenuItemClick = (index: number) => {
    setActiveIndex(index)
    // 模拟点击反馈，例如导航到其他页面
    console.log(`Menu item ${index} clicked: ${menuItems[index].text}`)
    // 0.3秒后重置 activeIndex 状态，以移除波纹效果
    setTimeout(() => setActiveIndex(null), 300)
  }

  return (
    <View className='profile'>
      <View className='header'>
        <View className='user-info'>
          <Avatar size='large' src='https://example.com/avatar.jpg' /> {/* 使用 src 替代 icon */}
          <View className='user-details'>
            <Text className='username'>用户名</Text>
            <Text className='user-id'>ID: 123456</Text>
          </View>
          <ArrowRight /> {/* 修复未定义的 Right 组件 */}
        </View>
        
        {/* 通知栏 */}
        <NoticeBar
          content='您的订单已发货，预计明天送达'
          closeable
          scrollable
        />
      </View>

      <View className='menu-section'>
        {menuItems.map((item, index) => (
          <View
            key={index}
            className={`ripple-container ${activeIndex === index ? 'ripple-active' : ''}`} // 添加 ripple 样式类
            onClick={() => handleMenuItemClick(index)} // 绑定点击事件
          >
            <Cell
              title={
                <View className='menu-item-left'>
                  <Text className='menu-icon'>{item.icon}</Text>
                  <Text className='menu-text'>{item.text}</Text>
                </View>
              }
              align='center'
              arrow={item.arrow}
            />
          </View>
        ))}
      </View>

      <View className='logout-section'>
        <Button type='primary' block>退出登录</Button>
      </View>
    </View>
  )
}
