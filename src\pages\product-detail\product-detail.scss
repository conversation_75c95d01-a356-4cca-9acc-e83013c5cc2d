.product-detail {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120px;
}

.image-section {
  position: relative;
  background-color: #fff;
}

.main-image {
  width: 100%;
  height: 750px;
}

.image-thumbnails {
  display: flex;
  padding: 20px;
  background-color: #fff;
}

.thumbnail {
  width: 120px;
  height: 120px;
  margin-right: 20px;
  border-radius: 12px;
  opacity: 0.6;
}

.thumbnail.active {
  opacity: 1;
  border: 2px solid #ff6b00;
}

.product-info {
  background-color: #fff;
  padding: 30px 20px;
  margin-bottom: 20px;
}

.product-name {
  font-size: 36px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.product-subtitle {
  font-size: 28px;
  color: #666;
  display: block;
  margin-bottom: 20px;
}

.price-section {
  margin-bottom: 20px;
}

.current-price {
  font-size: 40px;
  color: #ff6b00;
  font-weight: bold;
  margin-right: 20px;
}

.original-price {
  font-size: 28px;
  color: #999;
  text-decoration: line-through;
}

.rating-section {
  display: flex;
  align-items: center; /* 垂直居中对齐 */
  margin-bottom: 20px;
}

.rating-text {
  font-size: 28px;
  color: #ff6b00;
  margin: 0 10px; /* 调整评分文本的左右间距 */
}

.sales {
  font-size: 28px;
  color: #666;
}

.delivery-info {
  font-size: 28px;
  color: #666;
}

.detail-section, .nutrition-section, .gallery-section, .review-section {
  background-color: #fff;
  padding: 30px 20px;
  margin-bottom: 20px;
}

.section-title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20px;
}

.detail-item {
  font-size: 28px;
  color: #666;
  margin-bottom: 15px;
}

.nutrition-info {
  font-size: 28px;
  color: #666;
  line-height: 1.5;
}

.gallery-scroll {
  white-space: nowrap;
}

.gallery-container {
  display: inline-block;
}

.gallery-image {
  width: 300px;
  height: 300px;
  border-radius: 12px;
  margin-right: 20px;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.more-link {
  font-size: 26px;
  color: #999;
}

.review-item {
  margin-bottom: 20px;
}

.reviewer {
  font-size: 26px;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.review-content {
  font-size: 28px;
  color: #666;
  line-height: 1.5;
}

.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  height: 100px;
  background-color: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  font-size: 32px;
  font-weight: bold;
}

.cart-button {
  background-color: #ffd8bf;
  color: #ff6b00;
}

.buy-button {
  background-color: #ff6b00;
  color: #fff;
}