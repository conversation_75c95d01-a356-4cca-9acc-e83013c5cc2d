import { useState } from 'react' // 引入 useState
import { View, Text, Image, ScrollView } from '@tarojs/components'
import { useLoad } from '@tarojs/taro'
import { Rate } from '@nutui/nutui-react-taro' // 引入 NutUI 的 Rate 组件
import '@nutui/nutui-react-taro/dist/style.css' // 引入样式
import './product-detail.scss'

interface ProductDetail {
  id: number
  name: string
  subtitle: string
  price: number
  originalPrice?: number
  unit: string
  images: string[]
  detailImages: string[]
  origin: string
  storageMethod: string
  nutritionalInfo: string
  specs: string
  description: string
  detailDescription: string
  salesCount: number
  rating: number
  ratingCount: number
}

export default function ProductDetail() {
  useLoad(() => {
    console.log('Product detail page loaded.')
  })

  const product: ProductDetail = {
    id: 1,
    name: '新疆红富士苹果',
    subtitle: '脆甜多汁，营养丰富',
    price: 12.8,
    originalPrice: 15.8,
    unit: '斤',
    images: [
      'https://example.com/apple1.jpg',
      'https://example.com/apple2.jpg',
      'https://example.com/apple3.jpg'
    ],
    detailImages: [
      'https://example.com/apple-detail1.jpg',
      'https://example.com/apple-detail2.jpg'
    ],
    origin: '新疆阿克苏',
    storageMethod: '常温3-5天，冷藏7-10天',
    nutritionalInfo: '维生素C：每100g含4mg，膳食纤维：每100g含2.4g',
    specs: '单果重约200-250g',
    description: '新疆红富士苹果，产自天山北麓，日照充足，昼夜温差大，果实色泽鲜艳，肉质脆嫩，酸甜可口。',
    detailDescription: '<p>新疆红富士苹果产自天山北麓，这里有着独特的地理环境和气候条件。苹果树生长在海拔较高的山区，日照时间长，昼夜温差大，有利于糖分的积累。</p><p>果实色泽鲜艳，肉质脆嫩，酸甜可口，营养丰富。富含维生素C、膳食纤维等多种营养成分，是老少皆宜的健康水果。</p>',
    salesCount: 1200,
    rating: 4.8,
    ratingCount: 128
  }

  const [selectedImageIndex, setSelectedImageIndex] = useState(0) // 使用 state 管理选中的图片索引

  const handleImageClick = (index: number) => {
    setSelectedImageIndex(index) // 更新选中的图片索引
  }

  return (
    <View className='product-detail'>
      {/* 商品图片轮播区域 */}
      <View className='image-section'>
        <Image 
          className='main-image' 
          src={product.images[selectedImageIndex]} 
          mode='aspectFill' 
        />
        <View className='image-thumbnails'>
          {product.images.map((image, index) => (
            <Image 
              key={index}
              className={`thumbnail ${index === selectedImageIndex ? 'active' : ''}`}
              src={image}
              mode='aspectFill'
              onClick={() => handleImageClick(index)}
            />
          ))}
        </View>
      </View>

      {/* 商品基本信息 */}
      <View className='product-info'>
        <Text className='product-name'>{product.name}</Text>
        <Text className='product-subtitle'>{product.subtitle}</Text>
        
        <View className='price-section'>
          <Text className='current-price'>¥{product.price}/{product.unit}</Text>
          {product.originalPrice && (
            <Text className='original-price'>原价: ¥{product.originalPrice}/{product.unit}</Text>
          )}
        </View>
        
        {/* 使用 NutUI 的 Rate 组件替换原有的文本评分 */}
        <View className='rating-section'>
          <Rate value={product.rating} readonly size={14} />
          <Text className='rating-text'>{product.rating}分</Text>
          <Text className='sales'>| 已售{product.salesCount > 1000 ? `${(product.salesCount/1000).toFixed(1)}k` : product.salesCount}{product.unit}</Text>
        </View>
        
        <View className='delivery-info'>
          <Text>🚚 预计30分钟送达</Text>
        </View>
      </View>

      {/* 商品详情 */}
      <View className='detail-section'>
        <Text className='section-title'>📝 商品详情</Text>
        <View className='detail-item'>
          <Text>• 产地：{product.origin}</Text>
        </View>
        <View className='detail-item'>
          <Text>• 规格：{product.specs}</Text>
        </View>
        <View className='detail-item'>
          <Text>• 甜度：糖度14-16度</Text>
        </View>
        <View className='detail-item'>
          <Text>• 保存：{product.storageMethod}</Text>
        </View>
      </View>

      {/* 营养成分 */}
      <View className='nutrition-section'>
        <Text className='section-title'>📊 营养成分</Text>
        <Text className='nutrition-info'>{product.nutritionalInfo}</Text>
      </View>

      {/* 商品实拍 */}
      <View className='gallery-section'>
        <Text className='section-title'>📷 商品实拍</Text>
        <ScrollView 
          className='gallery-scroll' 
          scrollX 
          showScrollbar={false}
        >
          <View className='gallery-container'>
            {product.detailImages.map((image, index) => (
              <Image 
                key={index}
                className='gallery-image'
                src={image}
                mode='aspectFill'
              />
            ))}
          </View>
        </ScrollView>
      </View>

      {/* 用户评价 */}
      <View className='review-section'>
        <View className='review-header'>
          <Text className='section-title'>💬 用户评价 ({product.ratingCount}条)</Text>
          <Text className='more-link'>查看更多 ▶</Text>
        </View>
        <View className='review-item'>
          <Text className='reviewer'>👤 用户A</Text>
          <Text className='review-content'>很甜很脆，包装很好</Text>
        </View>
        <View className='review-item'>
          <Text className='reviewer'>👤 用户B</Text>
          <Text className='review-content'>果子很新鲜，会回购</Text>
        </View>
      </View>

      {/* 底部操作栏 */}
      <View className='action-bar'>
        <View className='action-button cart-button'>
          <Text>🛒 加入购物车</Text>
        </View>
        <View className='action-button buy-button'>
          <Text>立即购买</Text>
        </View>
      </View>
    </View>
  )
}