.product-list {
  background-color: #f5f5f5;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

.location {
  display: flex;
  align-items: center;
  font-size: 26px;
  color: #333;
}

.location-text {
  margin: 0 5px;
}

.search-box {
  display: flex;
  align-items: center;
  flex: 1;
  margin: 0 20px;
  padding: 8px 15px;
  background-color: #f5f5f5;
  border-radius: 18px;
  font-size: 24px;
  color: #999;
}

.search-placeholder {
  margin-left: 8px;
}

.custom-notice-bar {
  background-color: #fff; /* 与搜索栏相同的背景色 */
  padding: 10px 20px; /* 与搜索栏相似的内边距 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); /* 与搜索栏相同的阴影 */
  flex-shrink: 0; /* 防止在flex布局中被压缩 */
  border-bottom: 1px solid #eee; /* 底部添加边框，与搜索栏下方内容分隔 */
}

.custom-notice-bar ::v-deep .nut-noticebar__page-wrap {
  font-size: 26px; /* 字体大小与搜索栏文字一致 */
  color: #ff6b00; /* 使用项目主色调 */
}

.custom-notice-bar ::v-deep .nut-noticebar__page-wrap-text {
  display: flex;
  align-items: center;
}

.custom-notice-bar ::v-deep .nut-noticebar__vertical-icon {
  width: 28px; /* 调整图标大小 */
  height: 28px;
  margin-right: 10px; /* 图标与文字间距 */
}

.banner {
  margin: 15px 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.banner-image {
  width: 100%;
  height: 300px;
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.category-container {
  width: 180px;
  background-color: #fff;
  border-right: 1px solid #eee;
  overflow-y: auto;
}

.category-item {
  padding: 25px 15px;
  text-align: center;
  font-size: 26px;
  color: #666;
  border-bottom: 1px solid #f5f5f5;
}

.category-item.active {
  background-color: #ff6b00;
  color: #fff;
  font-weight: bold;
}

.product-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  flex-shrink: 0;
}

.category-title {
  font-size: 28px;
  font-weight: bold;
  color: #333;
}

.sort-options {
  display: flex;
}

.sort-option {
  font-size: 24px;
  color: #999;
  margin-left: 20px;
}

.sort-option.active {
  color: #ff6b00;
}

.product-scroll {
  flex: 1;
  overflow-y: auto;
}

.product-item {
  display: flex;
  background-color: #fff;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out; /* 添加阴影过渡 */
  border-radius: 8px; /* 添加圆角 */
  cursor: pointer; /* 添加鼠标指针 */
}

.product-item:active {
  transform: scale(0.98);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1); /* 点击时增强阴影 */
}

/* 为加入购物车按钮添加悬停和点击效果 */
.add-to-cart {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background-color: #ff6b00;
  border-radius: 50%;
  color: #fff;
  transition: background-color 0.3s ease, transform 0.1s ease; /* 添加过渡效果 */
}

.add-to-cart:hover {
  background-color: #e55a00; /* 悬停时加深背景色 */
}

.add-to-cart:active {
  transform: scale(0.9); /* 点击时缩小 */
}

.product-image {
  width: 140px;
  height: 140px;
  border-radius: 8px;
  margin-right: 20px;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.product-rating {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.rating-text {
  font-size: 24px;
  color: #ffcc00;
  margin: 0 10px;
}

.sales-text {
  font-size: 22px;
  color: #999;
}

.product-price {
  display: flex;
  align-items: center;
}

.current-price {
  font-size: 30px;
  color: #ff6b00;
  font-weight: bold;
  margin-right: 15px;
}

.original-price {
  font-size: 22px;
  color: #999;
  text-decoration: line-through;
}

.add-to-cart {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background-color: #ff6b00;
  border-radius: 50%;
  color: #fff;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
}

/* Popup styles */
.product-popup {
  padding: 20px;
}

.product-popup-details {
  margin-top: 10px;
  font-size: 24px;
  color: #666;
}

.product-popup-details Text {
  display: block;
  margin-bottom: 5px;
}

.product-popup-actions {
  margin-top: 20px;
}