.product-list {
  background-color: #f5f5f5;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

.location {
  display: flex;
  align-items: center;
  font-size: 26px;
  color: #333;
}

.location-text {
  margin: 0 5px;
}

.search-box {
  display: flex;
  align-items: center;
  flex: 1;
  margin: 0 20px;
  padding: 8px 15px;
  background-color: #f5f5f5;
  border-radius: 18px;
  font-size: 24px;
  color: #999;
}

.search-placeholder {
  margin-left: 8px;
}

/* 自定义通知栏样式 - 与搜索栏融合设计 */
.custom-notice-bar {
  background-color: #fff;
  padding: 12px 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
  border-top: 1px solid #f0f0f0;
  position: relative;
  overflow: hidden;
}

.notice-content {
  display: flex;
  align-items: center;
  height: 40px;
  position: relative;
}

.notice-icon {
  font-size: 28px;
  margin-right: 10px;
  color: #ff6b00;
  flex-shrink: 0;
}

.notice-text-container {
  flex: 1;
  height: 40px;
  overflow: hidden;
  position: relative;
}

.notice-text {
  font-size: 26px;
  color: #ff6b00;
  font-weight: 500;
  line-height: 40px;
  display: block;
  white-space: nowrap;
  animation: slideInUp 0.5s ease-in-out;
}

.notice-close {
  font-size: 24px;
  color: #999;
  margin-left: 10px;
  padding: 5px;
  flex-shrink: 0;
  transition: color 0.3s ease;
}

.notice-close:active {
  color: #666;
}

/* 滑入动画 */
@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 微信小程序兼容性优化 */
@media screen and (max-width: 750px) {
  .notice-text {
    font-size: 24px;
  }

  .notice-icon {
    font-size: 26px;
  }

  .notice-close {
    font-size: 22px;
  }
}

.banner {
  margin: 15px 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.banner-image {
  width: 100%;
  height: 300px;
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.category-container {
  width: 180px;
  background-color: #fff;
  border-right: 1px solid #eee;
  overflow-y: auto;
}

.category-item {
  padding: 25px 15px;
  text-align: center;
  font-size: 26px;
  color: #666;
  border-bottom: 1px solid #f5f5f5;
}

.category-item.active {
  background-color: #ff6b00;
  color: #fff;
  font-weight: bold;
}

.product-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  flex-shrink: 0;
}

.category-title {
  font-size: 28px;
  font-weight: bold;
  color: #333;
}

.sort-options {
  display: flex;
}

.sort-option {
  font-size: 24px;
  color: #999;
  margin-left: 20px;
}

.sort-option.active {
  color: #ff6b00;
}

.product-scroll {
  flex: 1;
  overflow-y: auto;
}

.product-item {
  display: flex;
  background-color: #fff;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out; /* 添加阴影过渡 */
  border-radius: 8px; /* 添加圆角 */
  cursor: pointer; /* 添加鼠标指针 */
}

.product-item:active {
  transform: scale(0.98);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1); /* 点击时增强阴影 */
}

/* 为加入购物车按钮添加悬停和点击效果 */
.add-to-cart {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background-color: #ff6b00;
  border-radius: 50%;
  color: #fff;
  transition: background-color 0.3s ease, transform 0.1s ease; /* 添加过渡效果 */
}

.add-to-cart:hover {
  background-color: #e55a00; /* 悬停时加深背景色 */
}

.add-to-cart:active {
  transform: scale(0.9); /* 点击时缩小 */
}

.product-image {
  width: 140px;
  height: 140px;
  border-radius: 8px;
  margin-right: 20px;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.product-rating {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.rating-text {
  font-size: 24px;
  color: #ffcc00;
  margin: 0 10px;
}

.sales-text {
  font-size: 22px;
  color: #999;
}

.product-price {
  display: flex;
  align-items: center;
}

.current-price {
  font-size: 30px;
  color: #ff6b00;
  font-weight: bold;
  margin-right: 15px;
}

.original-price {
  font-size: 22px;
  color: #999;
  text-decoration: line-through;
}

.add-to-cart {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background-color: #ff6b00;
  border-radius: 50%;
  color: #fff;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
}

/* Popup styles */
.product-popup {
  padding: 20px;
}

.product-popup-header {
  display: flex;
  margin-bottom: 20px;
}

.product-popup-image {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  margin-right: 15px;
  flex-shrink: 0;
}

.product-popup-info {
  flex: 1;
}

.product-popup-title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
  line-height: 1.4;
}

.product-popup-price {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.product-popup-details {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.product-popup-details text {
  font-size: 24px;
  color: #666;
}

.product-popup-actions {
  margin-top: 20px;
}

.product-popup-details {
  margin-top: 10px;
  font-size: 24px;
  color: #666;
}

.product-popup-details Text {
  display: block;
  margin-bottom: 5px;
}

.product-popup-actions {
  margin-top: 20px;
}