import { useState } from 'react'
import { View, Text, ScrollView, Image } from '@tarojs/components'
import Taro, { useLoad, useDidShow } from '@tarojs/taro' // 引入 Taro 和 useDidShow
import { Loading } from '@taroify/core'
import { Button, Popup } from '@nutui/nutui-react-taro'
import NoticeBar from '../../components/NoticeBar'
import '@taroify/core/index.scss'
import '@nutui/nutui-react-taro/dist/style.css'
import SkeletonProductList from '../../components/SkeletonProductList' // 引入骨架屏组件
import './product-list.scss'

interface Product {
  id: number
  name: string
  price: number
  originalPrice?: number
  unit: string
  image: string
  salesCount: number
  rating: number
}

interface Category {
  id: number
  name: string
}

export default function ProductList() {
  useLoad(() => {
    console.log('Product list page loaded.')
  })

  useDidShow(() => {
    // 页面显示时，通知自定义 TabBar 更新选中项为索引 0 ('商品' tab)
    Taro.eventCenter.trigger('TARO_TABBAR_CHANGE', 0)
  })

  const categories: Category[] = [
    { id: 1, name: '苹果' },
    { id: 2, name: '橙子' },
    { id: 3, name: '芒果' },
    { id: 4, name: '葡萄' },
    { id: 5, name: '香蕉' },
    { id: 6, name: '桃子' },
    { id: 7, name: '西瓜' },
    { id: 8, name: '梨' },
    { id: 9, name: '草莓' },
    { id: 10, name: '樱桃' }
  ]

  const allProducts: Product[] = [
    {
      id: 1,
      name: '新疆红富士苹果',
      price: 12.8,
      originalPrice: 15.8,
      unit: '斤',
      image: 'https://example.com/apple.jpg',
      salesCount: 1200,
      rating: 4.8
    },
    {
      id: 2,
      name: '山东红富士苹果',
      price: 10.8,
      unit: '斤',
      image: 'https://example.com/apple2.jpg',
      salesCount: 900,
      rating: 4.6
    },
    {
      id: 3,
      name: '赣南脐橙',
      price: 8.8,
      originalPrice: 10.8,
      unit: '斤',
      image: 'https://example.com/orange.jpg',
      salesCount: 800,
      rating: 4.7
    },
    {
      id: 4,
      name: '海南芒果',
      price: 15.8,
      unit: '斤',
      image: 'https://example.com/mango.jpg',
      salesCount: 650,
      rating: 4.9
    },
    {
      id: 5,
      name: '泰国金枕榴莲',
      price: 35.8,
      unit: '斤',
      image: 'https://example.com/durian.jpg',
      salesCount: 420,
      rating: 4.8
    },
    {
      id: 6,
      name: '智利车厘子',
      price: 88.8,
      unit: '斤',
      image: 'https://example.com/cherry.jpg',
      salesCount: 310,
      rating: 4.9
    }
  ]

  const [currentCategory, setCurrentCategory] = useState(1)
  const [loading, setLoading] = useState(false)
  const [showPopup, setShowPopup] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const [isFirstLoad, setIsFirstLoad] = useState(true) // 新增状态：是否首次加载

  // 通知栏消息数据
  const noticeMessages = [
    '🎉 新品上市：新疆红富士苹果，脆甜多汁，营养丰富！',
    '🔥 限时特惠：赣南脐橙，买一送一，仅限今日！',
    '✨ 品质保证：所有水果均来自优质产地，新鲜直达！',
    '🚚 免费配送：满99元免费送货上门，快速到达！'
  ]

  // 根据当前分类筛选商品
  const filteredProducts = allProducts.filter(product =>
    product.id <= 2 || currentCategory === 1 || currentCategory === 3
  )

  const handleCategoryClick = (categoryId: number) => {
    setLoading(true)
    setCurrentCategory(categoryId)
    // 模拟加载延迟
    setTimeout(() => {
      setLoading(false)
      setIsFirstLoad(false) // 首次加载完成
    }, 800) // 增加延迟以更好地展示骨架屏效果
  }

  const handleProductClick = (product: Product) => {
    setSelectedProduct(product)
    setShowPopup(true)
  }

  return (
    <View className='product-list'>
      {/* 顶部搜索栏 */}
      <View className='search-bar'>
        <View className='location'>
          <Text className='icon'>📍</Text>
          <Text className='location-text'>北京市朝阳区</Text>
          <Text className='icon'>▼</Text>
        </View>
        <View className='search-box'>
          <Text className='icon'>🔍</Text>
          <Text className='search-placeholder'>搜索商品</Text>
        </View>
        <Text className='icon'>🔔</Text>
      </View>

      {/* 多条消息通知栏 - 垂直滚动 */}
      <NoticeBar
        direction="vertical"
        list={noticeMessages}
        closeable
        duration={3000}
        height={40}
        onClick={(event) => {
          console.log('点击通知栏:', event)
        }}
        onItemClick={(_event, item, index) => {
          console.log('点击消息项:', item, index)
        }}
        onClose={(event) => {
          console.log('关闭通知栏:', event)
        }}
      />

      {/* 主内容区域 - 左侧分类，右侧商品列表 */}
      <View className='main-content'>
        {/* 左侧分类导航 - 可上下滑动 */}
        <ScrollView
          className='category-container'
          scrollY
          showScrollbar={false}
        >
          {categories.map(category => (
            <View
              key={category.id}
              className={`category-item ${currentCategory === category.id ? 'active' : ''}`}
              onClick={() => handleCategoryClick(category.id)}
            >
              <Text>{category.name}</Text>
            </View>
          ))}
        </ScrollView>

        {/* 右侧商品列表 */}
        <View className='product-container'>
          {/* 分类标题 */}
          <View className='category-header'>
            <Text className='category-title'>{categories.find(c => c.id === currentCategory)?.name}</Text>
            <View className='sort-options'>
              <Text className='sort-option active'>综合</Text>
              <Text className='sort-option'>销量</Text>
              <Text className='sort-option'>价格</Text>
            </View>
          </View>

          {/* 商品列表 */}
          {loading ? (
            isFirstLoad ? (
              // 首次加载时显示骨架屏
              <SkeletonProductList />
            ) : (
              // 切换分类时显示Loading
              <View className='loading-container'>
                <Loading />
                <Text>加载中...</Text>
              </View>
            )
          ) : (
            <ScrollView
              className='product-scroll'
              scrollY
              showScrollbar={false}
            >
              {filteredProducts.map(product => (
                <View
                  key={product.id}
                  className='product-item'
                  onClick={() => handleProductClick(product)}
                >
                  <Image className='product-image' src={product.image} mode='aspectFill' />
                  <View className='product-info'>
                    <Text className='product-name'>{product.name}</Text>
                    <View className='product-rating'>
                      <Text className='icon star'>⭐</Text>
                      <Text className='rating-text'>{product.rating}</Text>
                      <Text className='sales-text'>已售{product.salesCount > 1000 ? `${(product.salesCount/1000).toFixed(1)}k` : product.salesCount}{product.unit}</Text>
                    </View>
                    <View className='product-price'>
                      <Text className='current-price'>¥{product.price}/{product.unit}</Text>
                      {product.originalPrice && (
                        <Text className='original-price'>¥{product.originalPrice}/{product.unit}</Text>
                      )}
                    </View>
                  </View>
                  <View className='add-to-cart'>
                    <Text className='icon'>➕</Text>
                  </View>
                </View>
              ))}
            </ScrollView>
          )}
        </View>
      </View>

      {/* 商品详情弹窗 */}
      <Popup
        visible={showPopup}
        position="bottom"
        onClose={() => setShowPopup(false)}
        round
      >
        {selectedProduct && (
          <View className='product-popup'>
            <View className='product-popup-header'>
              <Image className='product-popup-image' src={selectedProduct.image} mode='aspectFill' />
              <View className='product-popup-info'>
                <Text className='product-popup-title'>{selectedProduct.name}</Text>
                <View className='product-popup-price'>
                  <Text className='current-price'>¥{selectedProduct.price}/{selectedProduct.unit}</Text>
                  {selectedProduct.originalPrice && (
                    <Text className='original-price'>¥{selectedProduct.originalPrice}/{selectedProduct.unit}</Text>
                  )}
                </View>
                <View className='product-popup-details'>
                  <Text>销量: {selectedProduct.salesCount}{selectedProduct.unit}</Text>
                  <Text>评分: {selectedProduct.rating} ⭐</Text>
                </View>
              </View>
            </View>
            <View className='product-popup-actions'>
              <Button type='primary' block>加入购物车</Button>
            </View>
          </View>
        )}
      </Popup>
    </View>
  )
}
